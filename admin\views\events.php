<?php
/**
 * Provincial Administration Manager - Events Management View
 * Enhanced Custom CRUD Interface with News-Style Implementation
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle CRUD operations
$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
$event_id = isset($_GET['event_id']) ? intval($_GET['event_id']) : 0;

// Get user information early for use throughout the file
$current_user_id = get_current_user_id();
$user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
$is_district_user = ($user_type === 'district');
$is_provincial_user = Provincial_User_Roles::user_has_provincial_access($current_user_id);

// Debug: Log what action is being requested
error_log('ESP Events - Requested Action: ' . $action);
error_log('ESP Events - Event ID: ' . $event_id);
error_log('ESP Events - Full URL: ' . $_SERVER['REQUEST_URI']);

// Show success messages for redirected updates
if (isset($_GET['updated']) && $_GET['updated'] == '1' && $event_id) {
    add_settings_error('esp_messages', 'event_updated', __('Event updated successfully!', 'esp-admin-manager'), 'success');
}
if (isset($_GET['created']) && $_GET['created'] == '1' && $event_id) {
    add_settings_error('esp_messages', 'event_created', __('Event created successfully!', 'esp-admin-manager'), 'success');
}

// Force default view if action is 'create' but we want to show the main dashboard
// This is a temporary fix to ensure the main dashboard shows by default
if ($action === 'create' && !isset($_GET['force_create'])) {
    // Check if this is the initial page load (not a form submission or explicit create request)
    if ($_SERVER['REQUEST_METHOD'] !== 'POST' && !isset($_POST['action'])) {
        $action = ''; // Reset to show main dashboard
        error_log('ESP Events - Forcing default dashboard view');
    }
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check nonce first
    if (!isset($_POST['esp_events_nonce']) || !wp_verify_nonce($_POST['esp_events_nonce'], 'esp_events_action')) {
        error_log('ESP Events - Nonce verification failed');
        error_log('ESP Events - Nonce present: ' . (isset($_POST['esp_events_nonce']) ? 'YES' : 'NO'));
        if (isset($_POST['esp_events_nonce'])) {
            error_log('ESP Events - Nonce value: ' . $_POST['esp_events_nonce']);
        }
        add_settings_error('esp_messages', 'nonce_error', __('Security check failed. Please try again.', 'esp-admin-manager'), 'error');
    } else {
    $post_action = sanitize_text_field($_POST['action']);
    error_log('ESP Events - Form submitted with action: ' . $post_action);
    error_log('ESP Events - POST data keys: ' . implode(', ', array_keys($_POST)));
    error_log('ESP Events - Event ID from POST: ' . (isset($_POST['event_id']) ? $_POST['event_id'] : 'NOT SET'));
    error_log('ESP Events - Event Title from POST: ' . (isset($_POST['event_title']) ? $_POST['event_title'] : 'NOT SET'));
    error_log('ESP Events - Current user can manage_options: ' . (current_user_can('manage_options') ? 'YES' : 'NO'));
    error_log('ESP Events - User type: ' . $user_type);

    if ($post_action === 'create' || $post_action === 'update' || $post_action === 'edit') {
        // Handle both 'update' and 'edit' actions as update operations
        $crud_action = ($post_action === 'create') ? 'create' : 'update';

        $result = handle_event_crud($crud_action, $_POST);
        if ($result['success']) {
            add_settings_error('esp_messages', 'event_saved', $result['message'], 'success');

            // Clear any object cache to ensure fresh data is loaded
            wp_cache_delete($result['event_id'], 'posts');
            wp_cache_delete($result['event_id'], 'post_meta');

            if ($crud_action === 'create') {
                $action = 'edit';
                $event_id = $result['event_id'];
                // Redirect to edit page after creation
                $redirect_url = admin_url('admin.php?page=provincial-admin-events&action=edit&event_id=' . $result['event_id'] . '&created=1');
                wp_redirect($redirect_url);
                exit;
            } else {
                // For updates, redirect to avoid form resubmission
                $action = 'edit';
                $event_id = intval($_POST['event_id']);
                $redirect_url = admin_url('admin.php?page=provincial-admin-events&action=edit&event_id=' . $event_id . '&updated=1');

                // Try to redirect, but if headers are already sent, set a flag for JavaScript redirect
                if (!headers_sent()) {
                    wp_redirect($redirect_url);
                    exit;
                } else {
                    // Fallback: Use JavaScript redirect
                    echo '<script>window.location.href = "' . esc_js($redirect_url) . '";</script>';
                    exit;
                }
            }
        } else {
            add_settings_error('esp_messages', 'event_error', $result['message'], 'error');
            // Stay on the same page to show the error
            if ($post_action !== 'create') {
                $action = 'edit';
                $event_id = intval($_POST['event_id']);
            }
        }
    }
    } // Close the nonce verification else block
}

// Handle delete action
if ($action === 'delete' && $event_id && wp_verify_nonce($_GET['_wpnonce'], 'delete_event_' . $event_id)) {
    $result = handle_event_delete($event_id);
    if ($result['success']) {
        add_settings_error('esp_messages', 'event_deleted', $result['message'], 'success');
        $action = '';
        $event_id = 0;
    } else {
        add_settings_error('esp_messages', 'event_delete_error', $result['message'], 'error');
    }
}

// Get events based on user type
if ($is_district_user && !current_user_can('manage_options')) {
    // District users see only events from their assigned districts
    $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);

    if (!empty($assigned_districts)) {
        // Get all events first, then filter by district assignment
        $all_events = get_posts(array(
            'post_type' => 'esp_event',
            'numberposts' => -1,
            'post_status' => 'any',
            'orderby' => 'meta_value',
            'meta_key' => '_esp_event_start_date',
            'order' => 'ASC'
        ));

        // Filter events by assigned districts - only show events linked to assigned districts
        $events = array();
        foreach ($all_events as $event) {
            $event_district_id = get_post_meta($event->ID, '_esp_district_id', true);
            // Only include events that are specifically linked to one of the assigned districts
            if (!empty($event_district_id) && in_array($event_district_id, $assigned_districts)) {
                $events[] = $event;
            }
        }
    } else {
        $events = array(); // No assigned districts
    }
} else {
    // Provincial users and administrators see all events
    $events = get_posts(array(
        'post_type' => 'esp_event',
        'numberposts' => -1,
        'post_status' => 'any',
        'orderby' => 'meta_value',
        'meta_key' => '_esp_event_start_date',
        'order' => 'ASC'
    ));
}

// CRUD Helper Functions
function handle_event_crud($action, $data) {
    $current_user_id = get_current_user_id();
    $user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
    $is_district_user = ($user_type === 'district');

    error_log('ESP Events CRUD - Action: ' . $action);
    error_log('ESP Events CRUD - User ID: ' . $current_user_id);
    error_log('ESP Events CRUD - User Type: ' . $user_type);
    error_log('ESP Events CRUD - Is District User: ' . ($is_district_user ? 'YES' : 'NO'));
    error_log('ESP Events CRUD - Can Manage Options: ' . (current_user_can('manage_options') ? 'YES' : 'NO'));

    // Validate required fields
    if (empty($data['event_title']) || empty($data['event_start_date'])) {
        error_log('ESP Events CRUD - Validation failed: Missing title or start date');
        error_log('ESP Events CRUD - Title: ' . (isset($data['event_title']) ? $data['event_title'] : 'NOT SET'));
        error_log('ESP Events CRUD - Start Date: ' . (isset($data['event_start_date']) ? $data['event_start_date'] : 'NOT SET'));
        return array('success' => false, 'message' => __('Title and start date are required.', 'esp-admin-manager'));
    }

    // Additional validation for update action
    if ($action === 'update' && (empty($data['event_id']) || !is_numeric($data['event_id']))) {
        error_log('ESP Events CRUD - Validation failed: Missing or invalid event_id for update');
        error_log('ESP Events CRUD - Event ID: ' . (isset($data['event_id']) ? $data['event_id'] : 'NOT SET'));
        return array('success' => false, 'message' => __('Invalid event ID for update.', 'esp-admin-manager'));
    }

    // Simplified permission logic: WP admin can do everything, district users need assigned districts
    if (!current_user_can('manage_options')) {
        // For non-admin users, validate district selection
        if ($is_district_user) {
            if (empty($data['event_district'])) {
                return array('success' => false, 'message' => __('District selection is required.', 'esp-admin-manager'));
            }

            $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
            if (!in_array(intval($data['event_district']), $assigned_districts)) {
                return array('success' => false, 'message' => __('You can only create events for your assigned districts.', 'esp-admin-manager'));
            }
        }
    }

    $post_data = array(
        'post_title' => sanitize_text_field($data['event_title']),
        'post_content' => wp_kses_post($data['event_content']),
        'post_status' => sanitize_text_field($data['event_status']),
        'post_type' => 'esp_event'
    );

    if ($action === 'create') {
        $post_data['post_author'] = $current_user_id;
        $event_id = wp_insert_post($post_data);
        $message = __('Event created successfully!', 'esp-admin-manager');
        error_log('ESP Events - Create result: ' . ($event_id ? 'Success (ID: ' . $event_id . ')' : 'Failed'));
    } else {
        $post_data['ID'] = intval($data['event_id']);
        error_log('ESP Events - Updating event with ID: ' . $post_data['ID']);
        error_log('ESP Events - Update data: ' . print_r($post_data, true));

        // Verify the post exists before updating
        $existing_post = get_post($post_data['ID']);
        if (!$existing_post) {
            error_log('ESP Events - Error: Post with ID ' . $post_data['ID'] . ' does not exist');
            return array('success' => false, 'message' => __('Event not found.', 'esp-admin-manager'));
        }

        // Verify the post is the correct type
        if ($existing_post->post_type !== 'esp_event') {
            error_log('ESP Events - Error: Post with ID ' . $post_data['ID'] . ' is not an event (type: ' . $existing_post->post_type . ')');
            return array('success' => false, 'message' => __('Invalid event type.', 'esp-admin-manager'));
        }

        // Perform the update
        error_log('ESP Events - About to call wp_update_post with data: ' . print_r($post_data, true));
        $event_id = wp_update_post($post_data, true); // true = return WP_Error on failure
        $message = __('Event updated successfully!', 'esp-admin-manager');
        error_log('ESP Events - wp_update_post returned: ' . print_r($event_id, true));
    }

    // Check for both WP_Error and 0/false return values
    if (is_wp_error($event_id)) {
        error_log('ESP Events - WP_Error: ' . $event_id->get_error_message());
        return array('success' => false, 'message' => $event_id->get_error_message());
    }

    if (!$event_id || $event_id === 0) {
        error_log('ESP Events - Update failed: wp_update_post returned 0 or false');
        return array('success' => false, 'message' => __('Failed to save event. Please check your data and try again.', 'esp-admin-manager'));
    }

    // Save meta fields
    error_log('ESP Events CRUD - Saving meta fields for event ID: ' . $event_id);

    // Save each meta field and verify it was saved
    $meta_fields = array(
        '_esp_event_start_date' => sanitize_text_field($data['event_start_date']),
        '_esp_event_end_date' => sanitize_text_field($data['event_end_date']),
        '_esp_event_location' => sanitize_text_field($data['event_location']),
        '_esp_event_contact' => sanitize_textarea_field($data['event_contact']),
        '_esp_district_id' => intval($data['event_district'])
    );

    foreach ($meta_fields as $meta_key => $meta_value) {
        $result = update_post_meta($event_id, $meta_key, $meta_value);
        error_log('ESP Events CRUD - Updated ' . $meta_key . ' = ' . $meta_value . ' (result: ' . ($result ? 'success' : 'failed') . ')');
    }

    // Handle image upload
    if (!empty($data['event_image_id'])) {
        set_post_thumbnail($event_id, intval($data['event_image_id']));
        error_log('ESP Events CRUD - Set thumbnail: ' . $data['event_image_id']);
    } else {
        delete_post_thumbnail($event_id);
        error_log('ESP Events CRUD - Deleted thumbnail');
    }

    error_log('ESP Events CRUD - Operation completed successfully');
    return array('success' => true, 'message' => $message, 'event_id' => $event_id);
}

function handle_event_delete($event_id) {
    $current_user_id = get_current_user_id();
    $user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
    $is_district_user = ($user_type === 'district');

    // Check permissions
    if ($is_district_user && !current_user_can('manage_options')) {
        $event_district_id = get_post_meta($event_id, '_esp_district_id', true);
        $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);

        if (!in_array($event_district_id, $assigned_districts)) {
            return array('success' => false, 'message' => __('You can only delete events from your assigned districts.', 'esp-admin-manager'));
        }
    }

    $result = wp_delete_post($event_id, true);

    if ($result) {
        return array('success' => true, 'message' => __('Event deleted successfully!', 'esp-admin-manager'));
    } else {
        return array('success' => false, 'message' => __('Failed to delete event.', 'esp-admin-manager'));
    }
}
?>

<div class="wrap">
    <div class="esp-admin-header">
        <h1><?php _e('Events Management', 'esp-admin-manager'); ?></h1>
        <p><?php _e('Manage government events, meetings, and public activities', 'esp-admin-manager'); ?></p>

        <?php if (current_user_can('manage_options')): ?>
        <div style="background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0;">
            <strong>🔓 Administrator Access:</strong> You have full CRUD access to all events including district-linked events.
            <a href="?page=provincial-admin-events&debug_caps=1" style="margin-left: 10px; font-size: 12px;">View Debug Info</a>
        </div>
        <?php endif; ?>
    </div>

    <?php settings_errors('esp_messages'); ?>

    <?php
    // Debug: Display current user capabilities for WP admins
    if (current_user_can('manage_options') && isset($_GET['debug_caps'])) {
        echo '<div style="background: #e7f3ff; border: 1px solid #0073aa; padding: 15px; margin: 20px 0; border-radius: 5px;">';
        echo '<h3>Debug: Current User Capabilities</h3>';
        echo '<p><strong>User ID:</strong> ' . get_current_user_id() . '</p>';
        echo '<p><strong>User Type:</strong> ' . $user_type . '</p>';
        echo '<p><strong>Is District User:</strong> ' . ($is_district_user ? 'Yes' : 'No') . '</p>';
        echo '<p><strong>Has manage_options:</strong> ' . (current_user_can('manage_options') ? 'Yes' : 'No') . '</p>';
        echo '<p><strong>Has manage_provincial_events:</strong> ' . (current_user_can('manage_provincial_events') ? 'Yes' : 'No') . '</p>';
        echo '<p><strong>Has manage_district_events:</strong> ' . (current_user_can('manage_district_events') ? 'Yes' : 'No') . '</p>';
        echo '<p><strong>Total Events Visible:</strong> ' . count($events) . '</p>';
        $current_user = wp_get_current_user();
        echo '<p><strong>User Roles:</strong> ' . implode(', ', $current_user->roles) . '</p>';
        echo '</div>';
    }
    ?>

    <?php
    // Display appropriate content based on action
    switch ($action) {
        case 'create':
            display_event_form('create');
            break;
        case 'edit':
            if ($event_id) {
                display_event_form('edit', $event_id);
            } else {
                display_events_list($events, $is_district_user);
            }
            break;
        case 'statistics':
            display_events_statistics($events);
            break;
        default:
            display_events_list($events, $is_district_user);
            break;
    }
    ?>
</div>

<?php
// Display Functions for CRUD Interface

function display_events_list($events, $is_district_user) {
    // Check if district user has assigned districts
    $current_user_id = get_current_user_id();
    $assigned_districts = array();
    $show_district_warning = false;

    if ($is_district_user && !current_user_can('manage_options')) {
        $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
        $show_district_warning = empty($assigned_districts);

        // Debug: Let's see what's happening
        error_log('ESP Events - District User ID: ' . $current_user_id);
        error_log('ESP Events - Assigned Districts: ' . print_r($assigned_districts, true));
        error_log('ESP Events - Show Warning: ' . ($show_district_warning ? 'true' : 'false'));
        error_log('ESP Events - Is District User: ' . ($is_district_user ? 'true' : 'false'));
        error_log('ESP Events - Can Manage Options: ' . (current_user_can('manage_options') ? 'true' : 'false'));

        // Get district names for display
        $district_names = array();
        if (!empty($assigned_districts)) {
            foreach ($assigned_districts as $district_id) {
                $district = get_post($district_id);
                if ($district) {
                    $district_names[] = $district->post_title;
                }
            }
        }
    }
    ?>

    <!-- Header Section matching News interface -->
    <div style="background: linear-gradient(135deg, #059669 0%, #047857 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; position: relative;">
        <div style="display: flex; align-items: center; gap: 15px;">
            <div style="font-size: 2.5rem;">📅</div>
            <div>
                <h1 style="margin: 0; font-size: 1.8rem; font-weight: bold;">
                    <?php echo $is_district_user ? __('My District Events', 'esp-admin-manager') : __('Events Management', 'esp-admin-manager'); ?>
                </h1>
                <p style="margin: 5px 0 0 0; opacity: 0.9; font-size: 1rem;">
                    <?php echo $is_district_user ? __('Manage events for your assigned district(s)', 'esp-admin-manager') : __('Manage government events, meetings, and public activities', 'esp-admin-manager'); ?>
                </p>
                <?php if ($is_district_user && !empty($district_names)): ?>
                <p style="margin: 8px 0 0 0; font-size: 0.9rem; opacity: 0.8;">
                    <strong><?php _e('Your Assigned Districts:', 'esp-admin-manager'); ?></strong> <?php echo esc_html(implode(', ', $district_names)); ?>
                </p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div style="background: #f8fafc; padding: 20px; border-bottom: 1px solid #e5e7eb;">
        <h3 style="margin: 0 0 15px 0; color: #374151; font-size: 1.1rem;"><?php _e('Quick Actions', 'esp-admin-manager'); ?></h3>
        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
            <?php if (!$show_district_warning): ?>
            <a href="?page=provincial-admin-events&action=create&force_create=1"
               style="background: #059669; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; font-size: 14px; font-weight: 500; display: inline-flex; align-items: center; gap: 6px;">
                ➕ <?php _e('Add New Event', 'esp-admin-manager'); ?>
            </a>
            <?php else: ?>
            <span style="background: #d1d5db; color: #6b7280; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; display: inline-flex; align-items: center; gap: 6px; cursor: not-allowed;"
                  title="<?php _e('You need assigned districts to create events', 'esp-admin-manager'); ?>">
                ➕ <?php _e('Add New Event', 'esp-admin-manager'); ?>
            </span>
            <?php endif; ?>

            <a href="<?php echo admin_url('edit.php?post_type=esp_event'); ?>"
               style="background: #6b7280; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; font-size: 14px; font-weight: 500; display: inline-flex; align-items: center; gap: 6px;">
                📋 <?php _e('Manage All Events', 'esp-admin-manager'); ?>
            </a>
        </div>
    </div>

    <?php if ($show_district_warning): ?>
    <div style="background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; margin: 0;">
        <div style="display: flex; align-items: center; gap: 10px;">
            <span style="font-size: 1.5rem;">⚠️</span>
            <div>
                <strong style="color: #92400e;"><?php _e('District Assignment Required', 'esp-admin-manager'); ?></strong>
                <p style="margin: 5px 0 0 0; color: #92400e;">
                    <?php _e('No assigned districts found. Contact an administrator to assign districts to your account.', 'esp-admin-manager'); ?>
                </p>
                <!-- Debug info -->
                <p style="margin: 10px 0 0 0; font-size: 12px; color: #6b7280; font-family: monospace;">
                    Debug: User ID: <?php echo $current_user_id; ?> |
                    Is District User: <?php echo $is_district_user ? 'Yes' : 'No'; ?> |
                    Assigned Districts: <?php echo empty($assigned_districts) ? 'None' : implode(', ', $assigned_districts); ?>
                </p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Recent Events Section -->
    <div style="background: white; padding: 20px;">
        <h3 style="margin: 0 0 20px 0; color: #374151; font-size: 1.1rem;"><?php _e('Recent Events', 'esp-admin-manager'); ?></h3>

        <?php if (!empty($events)): ?>
        <table style="width: 100%; border-collapse: collapse; background: white;">
            <thead>
                <tr style="background: #059669; color: white;">
                    <th style="padding: 12px; text-align: left; font-weight: 600; font-size: 13px;"><?php _e('Title', 'esp-admin-manager'); ?></th>
                    <th style="padding: 12px; text-align: left; font-weight: 600; font-size: 13px;"><?php _e('Date', 'esp-admin-manager'); ?></th>
                    <th style="padding: 12px; text-align: left; font-weight: 600; font-size: 13px;"><?php _e('Location', 'esp-admin-manager'); ?></th>
                    <th style="padding: 12px; text-align: left; font-weight: 600; font-size: 13px;"><?php _e('District', 'esp-admin-manager'); ?></th>
                    <th style="padding: 12px; text-align: left; font-weight: 600; font-size: 13px;"><?php _e('Featured', 'esp-admin-manager'); ?></th>
                    <th style="padding: 12px; text-align: left; font-weight: 600; font-size: 13px;"><?php _e('Status', 'esp-admin-manager'); ?></th>
                    <th style="padding: 12px; text-align: left; font-weight: 600; font-size: 13px;"><?php _e('Actions', 'esp-admin-manager'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($events as $event):
                    $start_date = get_post_meta($event->ID, '_esp_event_start_date', true);
                    $end_date = get_post_meta($event->ID, '_esp_event_end_date', true);
                    $location = get_post_meta($event->ID, '_esp_event_location', true);
                    $district_id = get_post_meta($event->ID, '_esp_district_id', true);
                    $featured = get_post_meta($event->ID, '_esp_event_featured', true); // Add featured support

                    // Get district name
                    $district_name = '';
                    if ($district_id) {
                        $district = get_post($district_id);
                        if ($district) {
                            $district_name = $district->post_title;
                        }
                    }

                    // Determine if event is upcoming, current, or past
                    $today = date('Y-m-d');
                    $event_status = 'past';
                    if ($start_date >= $today) {
                        $event_status = 'upcoming';
                    } elseif ($end_date && $end_date >= $today) {
                        $event_status = 'current';
                    }
                ?>
                <tr style="border-bottom: 1px solid #e5e7eb;">
                    <td style="padding: 15px 12px;">
                        <strong style="color: #1f2937; font-size: 14px; display: block; margin-bottom: 4px;">
                            <?php echo esc_html($event->post_title); ?>
                        </strong>
                        <div style="font-size: 12px; color: #6b7280;">
                            <?php
                            if ($event->post_excerpt) {
                                echo esc_html(wp_trim_words($event->post_excerpt, 15));
                            } else {
                                echo esc_html(wp_trim_words($event->post_content, 15));
                            }
                            ?>
                        </div>
                    </td>
                    <td style="padding: 15px 12px;">
                        <?php if ($start_date): ?>
                            <div style="font-weight: 600; color: #374151; font-size: 13px;">
                                <?php echo esc_html(date('M j, Y', strtotime($start_date))); ?>
                            </div>
                            <?php if ($end_date && $end_date !== $start_date): ?>
                                <div style="font-size: 11px; color: #6b7280;">
                                    to <?php echo esc_html(date('M j, Y', strtotime($end_date))); ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <span style="color: #9ca3af; font-style: italic; font-size: 12px;">No date set</span>
                        <?php endif; ?>
                    </td>
                    <td style="padding: 15px 12px; font-size: 13px;">
                        <?php if ($location): ?>
                            <?php echo esc_html($location); ?>
                        <?php else: ?>
                            <span style="color: #9ca3af;">—</span>
                        <?php endif; ?>
                    </td>
                    <td style="padding: 15px 12px;">
                        <?php if ($district_name): ?>
                            <span style="background: #059669; color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 500;">
                                <?php echo esc_html($district_name); ?>
                            </span>
                        <?php else: ?>
                            <span style="color: #9ca3af; font-size: 12px;"><?php _e('All Districts', 'esp-admin-manager'); ?></span>
                        <?php endif; ?>
                    </td>
                    <td style="padding: 15px 12px; text-align: center;">
                        <?php if ($featured === '1'): ?>
                            <span style="color: #f59e0b; font-size: 16px;" title="<?php _e('Featured Event', 'esp-admin-manager'); ?>">⭐</span>
                        <?php else: ?>
                            <span style="color: #d1d5db;">—</span>
                        <?php endif; ?>
                    </td>
                    <td style="padding: 15px 12px;">
                        <?php if ($event->post_status === 'publish'): ?>
                            <?php if ($event_status === 'upcoming'): ?>
                                <span style="background: #dbeafe; color: #1e40af; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 500;">
                                    <?php _e('Upcoming', 'esp-admin-manager'); ?>
                                </span>
                            <?php elseif ($event_status === 'current'): ?>
                                <span style="background: #dcfce7; color: #166534; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 500;">
                                    <?php _e('Current', 'esp-admin-manager'); ?>
                                </span>
                            <?php else: ?>
                                <span style="background: #f3f4f6; color: #6b7280; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 500;">
                                    <?php _e('Past', 'esp-admin-manager'); ?>
                                </span>
                            <?php endif; ?>
                        <?php else: ?>
                            <span style="background: #fef3c7; color: #92400e; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 500;">
                                <?php echo esc_html(ucfirst($event->post_status)); ?>
                            </span>
                        <?php endif; ?>
                    </td>
                    <td style="padding: 15px 12px;">
                        <div style="display: flex; gap: 5px;">
                            <a href="?page=provincial-admin-events&action=edit&event_id=<?php echo $event->ID; ?>"
                               style="background: #059669; color: white; padding: 4px 8px; border-radius: 4px; text-decoration: none; font-size: 11px; font-weight: 500;">
                                <?php _e('Edit', 'esp-admin-manager'); ?>
                            </a>
                            <a href="?page=provincial-admin-events&action=delete&event_id=<?php echo $event->ID; ?>&_wpnonce=<?php echo wp_create_nonce('delete_event_' . $event->ID); ?>"
                               style="background: #dc2626; color: white; padding: 4px 8px; border-radius: 4px; text-decoration: none; font-size: 11px; font-weight: 500;"
                               onclick="return confirm('<?php _e('Are you sure you want to delete this event?', 'esp-admin-manager'); ?>')">
                                <?php _e('Delete', 'esp-admin-manager'); ?>
                            </a>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <div style="text-align: center; padding: 40px 20px; color: #6b7280;">
            <div style="font-size: 3rem; margin-bottom: 15px;">📅</div>
            <p style="margin: 0; font-size: 14px;"><?php _e('No events found. Click "Add New Event" to get started.', 'esp-admin-manager'); ?></p>
        </div>
        <?php endif; ?>
    </div>

    <!-- Event Statistics Section -->
    <div style="background: white; padding: 20px; border-top: 1px solid #e5e7eb;">
        <h3 style="margin: 0 0 20px 0; color: #374151; font-size: 1.1rem;"><?php _e('Event Statistics', 'esp-admin-manager'); ?></h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
            <div style="text-align: center; padding: 15px; background: #f8fafc; border-radius: 8px;">
                <div style="font-size: 2rem; font-weight: bold; color: #059669; margin-bottom: 5px;">
                    <?php echo count($events); ?>
                </div>
                <div style="font-size: 13px; color: #6b7280; font-weight: 500;">
                    <?php _e('Total Events', 'esp-admin-manager'); ?>
                </div>
            </div>

            <div style="text-align: center; padding: 15px; background: #f8fafc; border-radius: 8px;">
                <div style="font-size: 2rem; font-weight: bold; color: #2563eb; margin-bottom: 5px;">
                    <?php
                    $upcoming = 0;
                    $today = date('Y-m-d');
                    foreach ($events as $event) {
                        $start_date = get_post_meta($event->ID, '_esp_event_start_date', true);
                        if ($start_date >= $today && $event->post_status === 'publish') {
                            $upcoming++;
                        }
                    }
                    echo $upcoming;
                    ?>
                </div>
                <div style="font-size: 13px; color: #6b7280; font-weight: 500;">
                    <?php _e('Upcoming Events', 'esp-admin-manager'); ?>
                </div>
            </div>

            <div style="text-align: center; padding: 15px; background: #f8fafc; border-radius: 8px;">
                <div style="font-size: 2rem; font-weight: bold; color: #16a34a; margin-bottom: 5px;">
                    <?php echo count(array_filter($events, function($event) { return $event->post_status === 'publish'; })); ?>
                </div>
                <div style="font-size: 13px; color: #6b7280; font-weight: 500;">
                    <?php _e('Published', 'esp-admin-manager'); ?>
                </div>
            </div>

            <div style="text-align: center; padding: 15px; background: #f8fafc; border-radius: 8px;">
                <div style="font-size: 2rem; font-weight: bold; color: #dc2626; margin-bottom: 5px;">
                    <?php
                    $this_month = 0;
                    $current_month = date('Y-m');
                    foreach ($events as $event) {
                        $start_date = get_post_meta($event->ID, '_esp_event_start_date', true);
                        if ($start_date && substr($start_date, 0, 7) === $current_month) {
                            $this_month++;
                        }
                    }
                    echo $this_month;
                    ?>
                </div>
                <div style="font-size: 13px; color: #6b7280; font-weight: 500;">
                    <?php _e('This Month', 'esp-admin-manager'); ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Section -->
    <div style="background: white; padding: 20px; border-top: 1px solid #e5e7eb;">
        <h3 style="margin: 0 0 15px 0; color: #374151; font-size: 1.1rem;"><?php _e('Preview', 'esp-admin-manager'); ?></h3>
        <p style="margin: 0 0 15px 0; color: #6b7280; font-size: 14px;"><?php _e('This is how the events will appear on your website:', 'esp-admin-manager'); ?></p>

        <div style="border: 1px solid #d1d5db; padding: 20px; background: #f9fafb; border-radius: 8px; max-height: 400px; overflow-y: auto;">
            <?php echo do_shortcode('[dakoii_events limit="5"]'); ?>
        </div>

        <div style="margin-top: 15px; padding: 15px; background: #f8fafc; border-radius: 6px;">
            <p style="margin: 0 0 10px 0; font-weight: 600; color: #374151;">
                <?php _e('Shortcode:', 'esp-admin-manager'); ?>
                <code style="background: #e5e7eb; padding: 2px 6px; border-radius: 4px; font-family: monospace;">[dakoii_prov_admin_events]</code>
                <span style="color: #6b7280; font-weight: normal; margin-left: 10px;"><?php _e('(Primary)', 'esp-admin-manager'); ?></span>
            </p>
            <p style="margin: 0; font-size: 13px; color: #6b7280;">
                <?php _e('or', 'esp-admin-manager'); ?>
                <code style="background: #e5e7eb; padding: 2px 6px; border-radius: 4px; font-family: monospace;">[dakoii_events]</code>,
                <code style="background: #e5e7eb; padding: 2px 6px; border-radius: 4px; font-family: monospace;">[esp_events]</code>
            </p>
        </div>
    </div>
    <?php
}

function display_event_form($mode, $event_id = 0) {
    $current_user_id = get_current_user_id();
    $user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
    $is_district_user = ($user_type === 'district');

    // Get event data for edit mode
    $event_data = array(
        'title' => '',
        'content' => '',
        'start_date' => '',
        'end_date' => '',
        'location' => '',
        'contact' => '',
        'district_id' => '',
        'status' => 'publish',
        'image_id' => ''
    );

    if ($mode === 'edit' && $event_id) {
        // Clear cache to ensure we get fresh data
        wp_cache_delete($event_id, 'posts');
        wp_cache_delete($event_id, 'post_meta');

        $event = get_post($event_id);
        if ($event) {
            $event_data = array(
                'title' => $event->post_title,
                'content' => $event->post_content,
                'start_date' => get_post_meta($event_id, '_esp_event_start_date', true),
                'end_date' => get_post_meta($event_id, '_esp_event_end_date', true),
                'location' => get_post_meta($event_id, '_esp_event_location', true),
                'contact' => get_post_meta($event_id, '_esp_event_contact', true),
                'district_id' => get_post_meta($event_id, '_esp_district_id', true),
                'status' => $event->post_status,
                'image_id' => get_post_thumbnail_id($event_id)
            );

            // Debug: Log the loaded event data
            error_log('ESP Events - Loaded event data for ID ' . $event_id . ': ' . print_r($event_data, true));
        } else {
            error_log('ESP Events - Failed to load event with ID: ' . $event_id);
        }
    }

    // Get available districts and check if district user has assignments
    $available_districts = array();
    $district_user_has_assignments = true;

    if ($is_district_user && !current_user_can('manage_options')) {
        $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
        if (!empty($assigned_districts)) {
            $available_districts = get_posts(array(
                'post_type' => 'esp_district',
                'numberposts' => -1,
                'post_status' => 'publish',
                'post__in' => $assigned_districts,
                'orderby' => 'title',
                'order' => 'ASC'
            ));
        } else {
            $district_user_has_assignments = false;
        }
    } else {
        $available_districts = get_posts(array(
            'post_type' => 'esp_district',
            'numberposts' => -1,
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        ));
    }
    ?>
    <div class="esp-event-form">
        <h2 style="color: #1f2937; margin-bottom: 25px;">
            <?php echo $mode === 'create' ? __('Create New Event', 'esp-admin-manager') : __('Edit Event', 'esp-admin-manager'); ?>
        </h2>

        <?php if ($is_district_user && !$district_user_has_assignments && $mode === 'create'): ?>
        <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
            <div style="display: flex; align-items: flex-start; gap: 15px;">
                <span style="font-size: 2rem;">⚠️</span>
                <div>
                    <h3 style="color: #92400e; margin: 0 0 10px 0;"><?php _e('District Assignment Required', 'esp-admin-manager'); ?></h3>
                    <p style="margin: 0 0 15px 0; color: #92400e; line-height: 1.6;">
                        <?php _e('You need to have assigned districts to create events. Please contact an administrator to assign districts to your account before creating events.', 'esp-admin-manager'); ?>
                    </p>
                    <div style="background: #fbbf24; padding: 10px; border-radius: 6px; margin-top: 15px;">
                        <strong style="color: #92400e;"><?php _e('What you can do:', 'esp-admin-manager'); ?></strong>
                        <ul style="margin: 8px 0 0 20px; color: #92400e;">
                            <li><?php _e('Contact your system administrator', 'esp-admin-manager'); ?></li>
                            <li><?php _e('Request district assignment for your account', 'esp-admin-manager'); ?></li>
                            <li><?php _e('View existing events in the Events Overview', 'esp-admin-manager'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($mode === 'edit'): ?>
        <div style="background: #e7f3ff; border: 1px solid #0073aa; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: #0073aa;">🔧 Debug Information</h4>
            <p><strong>Event ID:</strong> <?php echo $event_id; ?></p>
            <p><strong>Form Action URL:</strong> <?php echo esc_url(admin_url('admin.php?page=provincial-admin-events')); ?></p>
            <p><strong>Current User Can Manage Options:</strong> <?php echo current_user_can('manage_options') ? 'YES' : 'NO'; ?></p>
            <p><strong>Is District User:</strong> <?php echo $is_district_user ? 'YES' : 'NO'; ?></p>
            <p><strong>Event Data Loaded:</strong> <?php echo !empty($event_data['title']) ? 'YES' : 'NO'; ?></p>
            <p><strong>Event Title:</strong> <?php echo esc_html($event_data['title']); ?></p>
            <p><strong>Event Status:</strong> <?php echo esc_html($event_data['status']); ?></p>
            <p><strong>Start Date:</strong> <?php echo esc_html($event_data['start_date']); ?></p>
            <p><strong>District ID:</strong> <?php echo esc_html($event_data['district_id']); ?></p>
            <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
            <p style="color: #d63384;"><strong>POST Request Detected:</strong> Check error logs for detailed processing info</p>
            <?php endif; ?>

            <!-- Database Verification -->
            <?php
            $db_event = get_post($event_id);
            $db_start_date = get_post_meta($event_id, '_esp_event_start_date', true);
            $db_district_id = get_post_meta($event_id, '_esp_district_id', true);
            ?>
            <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #0073aa;">
                <h5 style="margin: 0 0 10px 0; color: #0073aa;">Database Values (Fresh Query)</h5>
                <p><strong>DB Title:</strong> <?php echo $db_event ? esc_html($db_event->post_title) : 'NOT FOUND'; ?></p>
                <p><strong>DB Status:</strong> <?php echo $db_event ? esc_html($db_event->post_status) : 'NOT FOUND'; ?></p>
                <p><strong>DB Start Date:</strong> <?php echo esc_html($db_start_date); ?></p>
                <p><strong>DB District ID:</strong> <?php echo esc_html($db_district_id); ?></p>
            </div>
        </div>
        <?php endif; ?>

        <form method="post" action="<?php echo esc_url(admin_url('admin.php?page=provincial-admin-events')); ?>" <?php echo ($is_district_user && !$district_user_has_assignments && $mode === 'create') ? 'style="opacity: 0.6; pointer-events: none;"' : ''; ?>>
            <?php wp_nonce_field('esp_events_action', 'esp_events_nonce'); ?>
            <input type="hidden" name="action" value="<?php echo $mode; ?>">
            <?php if ($mode === 'edit'): ?>
                <input type="hidden" name="event_id" value="<?php echo $event_id; ?>">
            <?php endif; ?>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin: 20px 0;">
                <div style="display: flex; flex-direction: column; margin-bottom: 20px;">
                    <label style="font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;" for="event_title">
                        <?php _e('Event Title', 'esp-admin-manager'); ?> <span style="color: #ef4444;">*</span>
                    </label>
                    <input type="text" id="event_title" name="event_title"
                           style="padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; transition: border-color 0.2s ease; width: 100%; box-sizing: border-box;"
                           value="<?php echo esc_attr($event_data['title']); ?>" required>
                </div>

                <div style="display: flex; flex-direction: column; margin-bottom: 20px;">
                    <label style="font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;" for="event_status">
                        <?php _e('Status', 'esp-admin-manager'); ?>
                    </label>
                    <select id="event_status" name="event_status"
                            style="padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; transition: border-color 0.2s ease; width: 100%; box-sizing: border-box; background: white;">
                        <option value="publish" <?php selected($event_data['status'], 'publish'); ?>><?php _e('Published', 'esp-admin-manager'); ?></option>
                        <option value="draft" <?php selected($event_data['status'], 'draft'); ?>><?php _e('Draft', 'esp-admin-manager'); ?></option>
                        <option value="private" <?php selected($event_data['status'], 'private'); ?>><?php _e('Private', 'esp-admin-manager'); ?></option>
                    </select>
                </div>

                <div style="display: flex; flex-direction: column; margin-bottom: 20px;">
                    <label style="font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;" for="event_start_date">
                        <?php _e('Start Date', 'esp-admin-manager'); ?> <span style="color: #ef4444;">*</span>
                    </label>
                    <input type="date" id="event_start_date" name="event_start_date"
                           style="padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; transition: border-color 0.2s ease; width: 100%; box-sizing: border-box;"
                           value="<?php echo esc_attr($event_data['start_date']); ?>" required>
                </div>

                <div style="display: flex; flex-direction: column; margin-bottom: 20px;">
                    <label style="font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;" for="event_end_date">
                        <?php _e('End Date', 'esp-admin-manager'); ?>
                    </label>
                    <input type="date" id="event_end_date" name="event_end_date"
                           style="padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; transition: border-color 0.2s ease; width: 100%; box-sizing: border-box;"
                           value="<?php echo esc_attr($event_data['end_date']); ?>">
                    <small style="color: #6b7280; margin-top: 4px; font-size: 12px;"><?php _e('Leave empty for single-day events', 'esp-admin-manager'); ?></small>
                </div>

                <div style="display: flex; flex-direction: column; margin-bottom: 20px;">
                    <label style="font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;" for="event_location">
                        <?php _e('Location', 'esp-admin-manager'); ?>
                    </label>
                    <input type="text" id="event_location" name="event_location"
                           style="padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; transition: border-color 0.2s ease; width: 100%; box-sizing: border-box;"
                           value="<?php echo esc_attr($event_data['location']); ?>"
                           placeholder="<?php _e('e.g., City Hall, Conference Room A', 'esp-admin-manager'); ?>">
                </div>

                <div style="display: flex; flex-direction: column; margin-bottom: 20px;">
                    <label style="font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;" for="event_district">
                        <?php _e('District', 'esp-admin-manager'); ?>
                        <?php if ($is_district_user): ?> <span style="color: #ef4444;">*</span><?php endif; ?>
                    </label>
                    <select id="event_district" name="event_district"
                            style="padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; transition: border-color 0.2s ease; width: 100%; box-sizing: border-box; background: white;"
                            <?php echo $is_district_user ? 'required' : ''; ?>>
                        <?php if (!$is_district_user): ?>
                            <option value=""><?php _e('All Districts (Optional)', 'esp-admin-manager'); ?></option>
                        <?php else: ?>
                            <option value=""><?php _e('Select District', 'esp-admin-manager'); ?></option>
                        <?php endif; ?>
                        <?php foreach ($available_districts as $district): ?>
                            <option value="<?php echo esc_attr($district->ID); ?>" <?php selected($event_data['district_id'], $district->ID); ?>>
                                <?php echo esc_html($district->post_title); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <?php if ($is_district_user): ?>
                        <small style="color: #6b7280; margin-top: 4px; font-size: 12px;"><?php _e('Required: Select the district this event belongs to', 'esp-admin-manager'); ?></small>
                    <?php endif; ?>
                </div>

                <!-- Event Image Upload -->
                <div style="display: flex; flex-direction: column; margin-bottom: 20px; grid-column: 1 / -1;">
                    <label style="font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;">
                        <?php _e('Event Image', 'esp-admin-manager'); ?>
                    </label>
                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <input type="hidden" id="event-image-id" name="event_image_id" value="<?php echo esc_attr($event_data['image_id']); ?>">

                        <div id="image-preview" style="<?php echo $event_data['image_id'] ? '' : 'display: none;'; ?>">
                            <?php if ($event_data['image_id']):
                                $image_url = wp_get_attachment_image_url($event_data['image_id'], 'medium');
                            ?>
                                <div style="position: relative; display: inline-block;">
                                    <img id="preview-image" src="<?php echo esc_url($image_url); ?>" alt="Event Image Preview"
                                         style="max-width: 300px; max-height: 200px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <button type="button" id="remove-image-btn"
                                            style="position: absolute; top: 8px; right: 8px; background: #dc2626; color: white; border: none; border-radius: 50%; width: 24px; height: 24px; cursor: pointer; font-size: 12px; display: flex; align-items: center; justify-content: center;">
                                        ✕
                                    </button>
                                </div>
                            <?php else: ?>
                                <div style="position: relative; display: inline-block;">
                                    <img id="preview-image" src="" alt="Event Image Preview"
                                         style="max-width: 300px; max-height: 200px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <button type="button" id="remove-image-btn"
                                            style="position: absolute; top: 8px; right: 8px; background: #dc2626; color: white; border: none; border-radius: 50%; width: 24px; height: 24px; cursor: pointer; font-size: 12px; display: flex; align-items: center; justify-content: center;">
                                        ✕
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>

                        <button type="button" id="upload-image-btn"
                                style="background: #6b7280; color: white; padding: 12px 20px; border: none; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; display: inline-flex; align-items: center; gap: 8px; width: fit-content; transition: background 0.2s ease;"
                                onmouseover="this.style.background='#4b5563'" onmouseout="this.style.background='#6b7280'">
                            📷 <?php _e('Choose Event Image', 'esp-admin-manager'); ?>
                        </button>

                        <small style="color: #6b7280; font-size: 12px;">
                            <?php _e('Upload an image to make your event more engaging. Recommended size: 800x400 pixels.', 'esp-admin-manager'); ?>
                        </small>
                    </div>
                </div>

                <div style="display: flex; flex-direction: column; margin-bottom: 20px; grid-column: 1 / -1;">
                    <label style="font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;" for="event_content">
                        <?php _e('Event Description', 'esp-admin-manager'); ?>
                    </label>
                    <textarea id="event_content" name="event_content" rows="6"
                              style="padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; transition: border-color 0.2s ease; width: 100%; box-sizing: border-box; resize: vertical; font-family: inherit;"
                              placeholder="<?php _e('Provide detailed information about the event...', 'esp-admin-manager'); ?>"><?php echo esc_textarea($event_data['content']); ?></textarea>
                </div>

                <div style="display: flex; flex-direction: column; margin-bottom: 20px; grid-column: 1 / -1;">
                    <label style="font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;" for="event_contact">
                        <?php _e('Contact Information', 'esp-admin-manager'); ?>
                    </label>
                    <textarea id="event_contact" name="event_contact" rows="3"
                              style="padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; transition: border-color 0.2s ease; width: 100%; box-sizing: border-box; resize: vertical; font-family: inherit;"
                              placeholder="<?php _e('Contact details for inquiries, RSVP information, etc.', 'esp-admin-manager'); ?>"><?php echo esc_textarea($event_data['contact']); ?></textarea>
                </div>
            </div>

            <div style="display: flex; justify-content: flex-end; gap: 15px; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                <a href="?page=provincial-admin-events"
                   style="background: #6b7280; color: white; padding: 12px 24px; border-radius: 6px; text-decoration: none; font-size: 14px; font-weight: 500; display: inline-flex; align-items: center; gap: 8px; transition: background 0.2s ease;">
                    ← <?php _e('Cancel', 'esp-admin-manager'); ?>
                </a>
                <?php if ($is_district_user && !$district_user_has_assignments && $mode === 'create'): ?>
                    <button type="button" disabled
                            style="background: #d1d5db; color: #6b7280; padding: 12px 24px; border-radius: 6px; border: none; font-size: 14px; font-weight: 500; display: inline-flex; align-items: center; gap: 8px; cursor: not-allowed;">
                        🔒 <?php _e('District Assignment Required', 'esp-admin-manager'); ?>
                    </button>
                <?php else: ?>
                    <?php if ($mode === 'edit'): ?>
                    <button type="button" onclick="testUpdate()"
                            style="background: #f59e0b; color: white; padding: 12px 24px; border: none; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; display: inline-flex; align-items: center; gap: 8px; transition: background 0.2s ease;">
                        🧪 <?php _e('Test Update', 'esp-admin-manager'); ?>
                    </button>
                    <?php endif; ?>

                    <button type="submit"
                            style="background: #059669; color: white; padding: 12px 24px; border-radius: 6px; border: none; font-size: 14px; font-weight: 500; display: inline-flex; align-items: center; gap: 8px; cursor: pointer; transition: background 0.2s ease;"
                            onmouseover="this.style.background='#047857'" onmouseout="this.style.background='#059669'">
                        <?php echo $mode === 'create' ? '✅ ' . __('Create Event', 'esp-admin-manager') : '💾 ' . __('Update Event', 'esp-admin-manager'); ?>
                    </button>
                <?php endif; ?>
            </div>
        </form>

        <?php if ($mode === 'edit'): ?>
        <script>
        function testUpdate() {
            // Add a timestamp to the title to test if update is working
            var titleField = document.getElementById('event_title');
            var currentTitle = titleField.value;
            var timestamp = new Date().toLocaleTimeString();

            if (!currentTitle.includes('[TEST')) {
                titleField.value = currentTitle + ' [TEST ' + timestamp + ']';
                alert('Added test timestamp to title. Now click "Update Event" to test if the update works.');
            } else {
                alert('Test timestamp already added. Click "Update Event" to save.');
            }
        }
        </script>
        <?php endif; ?>
    </div>
    <?php
}

function display_events_statistics($events) {
    $today = date('Y-m-d');
    $current_month = date('Y-m');

    // Calculate statistics
    $total_events = count($events);
    $published_events = count(array_filter($events, function($event) { return $event->post_status === 'publish'; }));
    $upcoming_events = 0;
    $current_events = 0;
    $past_events = 0;
    $this_month_events = 0;
    $draft_events = count(array_filter($events, function($event) { return $event->post_status === 'draft'; }));

    foreach ($events as $event) {
        $start_date = get_post_meta($event->ID, '_esp_event_start_date', true);
        $end_date = get_post_meta($event->ID, '_esp_event_end_date', true);

        if ($start_date) {
            // Count this month events
            if (substr($start_date, 0, 7) === $current_month) {
                $this_month_events++;
            }

            // Count by status
            if ($event->post_status === 'publish') {
                if ($start_date >= $today) {
                    $upcoming_events++;
                } elseif ($end_date && $end_date >= $today) {
                    $current_events++;
                } else {
                    $past_events++;
                }
            }
        }
    }
    ?>
    <div class="esp-events-statistics">
        <h2 style="color: #1f2937; margin-bottom: 30px;"><?php _e('Events Statistics & Analytics', 'esp-admin-manager'); ?></h2>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 25px; margin-bottom: 40px;">
            <div style="background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 10px;">📊</div>
                <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 5px;"><?php echo $total_events; ?></div>
                <div style="opacity: 0.9;"><?php _e('Total Events', 'esp-admin-manager'); ?></div>
            </div>

            <div style="background: linear-gradient(135deg, #10b981 0%, #047857 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 10px;">🚀</div>
                <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 5px;"><?php echo $upcoming_events; ?></div>
                <div style="opacity: 0.9;"><?php _e('Upcoming Events', 'esp-admin-manager'); ?></div>
            </div>

            <div style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 10px;">⏰</div>
                <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 5px;"><?php echo $current_events; ?></div>
                <div style="opacity: 0.9;"><?php _e('Current Events', 'esp-admin-manager'); ?></div>
            </div>

            <div style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 10px;">📅</div>
                <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 5px;"><?php echo $this_month_events; ?></div>
                <div style="opacity: 0.9;"><?php _e('This Month', 'esp-admin-manager'); ?></div>
            </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
            <div style="background: white; padding: 25px; border-radius: 12px; border: 1px solid #e5e7eb;">
                <h3 style="color: #1f2937; margin-bottom: 20px;"><?php _e('Event Status Breakdown', 'esp-admin-manager'); ?></h3>
                <div style="space-y: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #f3f4f6;">
                        <span style="color: #374151;"><?php _e('Published', 'esp-admin-manager'); ?></span>
                        <span style="font-weight: bold; color: #10b981;"><?php echo $published_events; ?></span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #f3f4f6;">
                        <span style="color: #374151;"><?php _e('Draft', 'esp-admin-manager'); ?></span>
                        <span style="font-weight: bold; color: #f59e0b;"><?php echo $draft_events; ?></span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #f3f4f6;">
                        <span style="color: #374151;"><?php _e('Past Events', 'esp-admin-manager'); ?></span>
                        <span style="font-weight: bold; color: #6b7280;"><?php echo $past_events; ?></span>
                    </div>
                </div>
            </div>

            <div style="background: white; padding: 25px; border-radius: 12px; border: 1px solid #e5e7eb;">
                <h3 style="color: #1f2937; margin-bottom: 20px;"><?php _e('Shortcode Usage', 'esp-admin-manager'); ?></h3>
                <div style="background: #f8fafc; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                    <code style="color: #1e40af; font-weight: 600;">[dakoii_prov_admin_events]</code>
                    <div style="font-size: 12px; color: #6b7280; margin-top: 5px;"><?php _e('Primary shortcode', 'esp-admin-manager'); ?></div>
                </div>
                <div style="font-size: 14px; color: #374151; line-height: 1.6;">
                    <strong><?php _e('Options:', 'esp-admin-manager'); ?></strong><br>
                    <code>limit="5"</code> - <?php _e('Show only 5 events', 'esp-admin-manager'); ?><br>
                    <code>upcoming_only="false"</code> - <?php _e('Show all events', 'esp-admin-manager'); ?><br>
                    <code>show_images="false"</code> - <?php _e('Hide images', 'esp-admin-manager'); ?>
                </div>
            </div>
        </div>

        <div style="background: #f8fafc; padding: 25px; border-radius: 12px; margin-top: 30px; border-left: 4px solid #3b82f6;">
            <h3 style="color: #1f2937; margin-bottom: 15px;">💡 <?php _e('Event Management Tips', 'esp-admin-manager'); ?></h3>
            <ul style="color: #374151; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><?php _e('Add events well in advance to give citizens time to plan', 'esp-admin-manager'); ?></li>
                <li><?php _e('Include clear, specific location information', 'esp-admin-manager'); ?></li>
                <li><?php _e('Provide detailed descriptions of what the event involves', 'esp-admin-manager'); ?></li>
                <li><?php _e('Update or remove events if they are cancelled or postponed', 'esp-admin-manager'); ?></li>
                <li><?php _e('Use the contact field for RSVP or inquiry information', 'esp-admin-manager'); ?></li>
                <li><?php _e('Consider adding photos after events for public engagement', 'esp-admin-manager'); ?></li>
            </ul>
        </div>

        <div style="margin-top: 30px; text-align: center;">
            <h3 style="color: #1f2937; margin-bottom: 20px;"><?php _e('Live Preview', 'esp-admin-manager'); ?></h3>
            <p style="color: #6b7280; margin-bottom: 20px;"><?php _e('This is how your events will appear on the website:', 'esp-admin-manager'); ?></p>
            <div style="border: 2px dashed #d1d5db; padding: 25px; background: #f9fafb; border-radius: 12px; max-height: 400px; overflow-y: auto;">
                <?php echo do_shortcode('[dakoii_events limit="3"]'); ?>
            </div>
        </div>
    </div>
    <?php
}
?>

<script>
jQuery(document).ready(function($) {
    let mediaUploader;

    // Media upload button
    $('#upload-image-btn').on('click', function(e) {
        e.preventDefault();

        if (mediaUploader) {
            mediaUploader.open();
            return;
        }

        mediaUploader = wp.media({
            title: '<?php _e('Choose Event Image', 'esp-admin-manager'); ?>',
            button: {
                text: '<?php _e('Use this image', 'esp-admin-manager'); ?>'
            },
            multiple: false,
            library: {
                type: 'image'
            }
        });

        mediaUploader.on('select', function() {
            const attachment = mediaUploader.state().get('selection').first().toJSON();
            $('#event-image-id').val(attachment.id);
            $('#preview-image').attr('src', attachment.url);
            $('#image-preview').show();
        });

        mediaUploader.open();
    });

    // Remove image button
    $('#remove-image-btn').on('click', function(e) {
        e.preventDefault();
        $('#event-image-id').val('');
        $('#image-preview').hide();
    });

    // Add focus styles to form inputs
    $('input, select, textarea').on('focus', function() {
        $(this).css({
            'border-color': '#059669',
            'box-shadow': '0 0 0 3px rgba(5, 150, 105, 0.1)'
        });
    }).on('blur', function() {
        $(this).css({
            'border-color': '#d1d5db',
            'box-shadow': 'none'
        });
    });
});
</script>
